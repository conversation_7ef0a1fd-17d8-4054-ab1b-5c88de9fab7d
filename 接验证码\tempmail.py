

import random
import re
import string
import time
import tkinter as tk
from datetime import datetime
from tkinter import ttk, messagebox, scrolledtext
import requests
from bs4 import BeautifulSoup
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class TempEmailManager:
    """临时邮箱管理及验证码获取类"""
    
    def __init__(self, domain=None):
        """初始化邮箱管理器"""
        # 加载配置中的域名，如果没有提供
        if domain is None:
            config = load_config()
            self.domain = config.get("temp_email_domain")
            # 确保一定有域名
            if not self.domain:
                raise ValueError("未设置临时邮箱域名，请在配置中指定")
        else:
            self.domain = domain
            
        self.username = None
        self.email = None
        print(f"使用临时邮箱域名: {self.domain}")
        
    def generate_random_username(self, length=10):
        """生成随机用户名"""
        letters = string.ascii_lowercase
        return ''.join(random.choice(letters) for i in range(length))
    
    def get_temp_email(self):
        """获取临时邮箱"""
        print("\n===== 临时邮箱获取 =====")
        
        # 生成随机用户名
        self.username = self.generate_random_username()
        self.email = f"{self.username}@{self.domain}"
        
        print(f"生成临时邮箱: {self.email}")
        return self.email
    
    def get_verification_code(self, timeout=120):
        """从临时邮箱获取验证码"""
        print("\n===== 临时邮箱验证码接收 =====")
        
        if not self.username or not self.email:
            print("错误: 未设置邮箱")
            return None
        
        # 构建URL
        url = f"https://mail-temp.com/{self.domain}/{self.username}"
        
        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        print(f"开始获取邮箱 {self.email} 的验证码...")
        
        # 最大重试次数改为8次
        max_attempts = 8  # 减少最大尝试次数
        attempt = 0
        
        # 创建具有重试机制的会话
        session = requests.Session()
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"],
            backoff_factor=0.5  # 减少重试等待时间
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Referer': 'https://mail-temp.com/'
        }
        
        while attempt < max_attempts:
            try:
                # 减少随机延迟
                time.sleep(1)
                
                # 获取页面内容，增加超时设置
                response = session.get(url, headers=headers, timeout=15, verify=False)
                response.raise_for_status()
                
                # 解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 检查是否有Cursor验证邮件并提取验证码
                if "cursor" in response.text.lower():
                    # 使用精确的前缀模式提取验证码
                    verify_pattern = r'Your verification code is (\d{6})'
                    code_match = re.search(verify_pattern, response.text)
                    
                    if code_match:
                        code = code_match.group(1)
                        print(f"已找到验证码: {code}")
                        return code
                
                attempt += 1
                if attempt < max_attempts:
                    wait_time = 2  # 固定等待时间，减少延迟
                    print(f"尝试 {attempt}/{max_attempts}: 未找到验证码，等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
            
            except Exception as e:
                print(f"获取验证码时出错: {e}")
                attempt += 1
                if attempt < max_attempts:
                    wait_time = 3  # 固定等待时间，减少延迟
                    print(f"尝试 {attempt}/{max_attempts}: 请求失败，等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
        
        print("达到最大尝试次数，未找到验证码")
        return None